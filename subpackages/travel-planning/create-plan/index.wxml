<!--创建旅行计划页面 - 革新设计-->
<view class="create-plan-container">
  <!-- 渐变背景 -->
  <view class="gradient-background"></view>

  <!-- 页面内容 -->
  <scroll-view class="content-scroll" scroll-y="true" enhanced="true" show-scrollbar="false">

    <!-- 魔法解析弹窗 -->
    <view wx:if="{{showMagicModal}}" class="form-modal magic-modal">
      <view class="modal-mask" bindtap="hideMagicModal"></view>
      <view class="modal-dialog magic-dialog">
        <view class="dialog-header magic-header">
          <view class="magic-title-container">
            <text class="magic-sparkle-icon">✨</text>
            <text class="dialog-title">魔法解析</text>
            <text class="magic-sparkle-icon">✨</text>
          </view>
          <text class="dialog-close" bindtap="hideMagicModal">×</text>
        </view>

        <view class="dialog-body magic-body">
          <!-- 支持平台展示 -->
          <view class="supported-platforms">
            <text class="platforms-title">支持平台</text>
            <view class="platforms-list">
              <view class="platform-item xiaohongshu">
                <text class="platform-icon">📱</text>
                <text class="platform-name">小红书</text>
              </view>
              <view class="platform-item wechat">
                <text class="platform-icon">💬</text>
                <text class="platform-name">微信公众号</text>
              </view>
              <view class="platform-item dianping">
                <text class="platform-icon">⭐</text>
                <text class="platform-name">大众点评</text>
              </view>
              <view class="platform-item mafengwo">
                <text class="platform-icon">🐝</text>
                <text class="platform-name">马蜂窝</text>
              </view>
            </view>
          </view>

          <!-- URL输入区域 -->
          <view class="url-input-section">
            <view class="input-label">
              <text class="label-text">粘贴链接</text>
              <text class="label-desc">复制小红书、公众号等平台的旅行攻略链接</text>
            </view>
            <view class="url-input-container">
              <textarea
                class="url-input"
                placeholder="粘贴链接，例如：https://www.xiaohongshu.com/..."
                placeholder-class="input-placeholder"
                value="{{magicUrl}}"
                bindinput="onMagicUrlInput"
                auto-height
                maxlength="500"
              />
              <view class="input-actions">
                <text class="paste-btn" bindtap="pasteFromClipboard">📋 粘贴</text>
                <text class="clear-btn" bindtap="clearMagicUrl" wx:if="{{magicUrl}}">🗑️</text>
              </view>
            </view>
          </view>

          <!-- 解析结果预览 -->
          <view wx:if="{{magicResult}}" class="magic-result-preview">
            <view class="result-header">
              <text class="result-title">解析结果</text>
              <text class="result-platform">{{magicResult.platform}}</text>
            </view>
            <view class="result-content">
              <text class="result-text">{{magicResult.title}}</text>
              <text class="result-locations">发现 {{magicResult.locations.length}} 个地点</text>
            </view>
          </view>
        </view>

        <view class="dialog-footer magic-footer">
          <button class="btn-cancel" bindtap="hideMagicModal">取消</button>
          <button class="btn-magic" bindtap="performMagicParse"
                  loading="{{magicParsing}}"
                  disabled="{{!magicUrl || magicParsing}}">
            <text class="magic-btn-text">
              {{magicParsing ? '解析中...' : '✨ 开始魔法解析'}}
            </text>
          </button>
        </view>
      </view>
    </view>

    <!-- 规划结果 -->
    <view wx:if="{{currentStep === 0 && smartPlanResult}}" class="result-container">
      <view class="result-status">
        <text class="status-icon">✅</text>
        <text class="status-text">规划完成</text>
      </view>

      <view class="result-content">
        <!-- 地图 -->
        <view class="map-section" bindtap="viewFullMap">
          <map
            id="result-map"
            class="result-map"
            latitude="{{smartPlanResult.mapData.center.latitude}}"
            longitude="{{smartPlanResult.mapData.center.longitude}}"
            scale="{{smartPlanResult.mapData.scale}}"
            markers="{{smartPlanResult.mapData.markers}}"
            polyline="{{smartPlanResult.mapData.polyline}}"
            show-location="{{false}}"
          />
          <view class="map-overlay">
            <text class="map-tip">点击查看完整地图</text>
          </view>
        </view>

        <!-- 行程信息 -->
        <view class="trip-info">
          <text class="trip-title">{{smartPlanResult.title}}</text>
          <view class="trip-stats">
            <text class="stat-item">{{smartPlanResult.duration}}天</text>
            <text class="stat-item">{{smartPlanResult.totalDistance}}km</text>
            <text class="stat-item">¥{{smartPlanResult.estimatedCost.total}}</text>
          </view>
        </view>

        <!-- 每日安排 -->
        <view class="daily-plan">
          <scroll-view class="plan-scroll" scroll-x>
            <view class="day-item" wx:for="{{smartPlanResult.dailyPlan}}" wx:key="day">
              <text class="day-title">第{{item.day}}天</text>
              <view class="day-spots">
                <text class="spot-name" wx:for="{{item.attractions}}" wx:key="id" wx:for-item="spot">
                  {{spot.name}}
                </text>
              </view>
            </view>
          </scroll-view>
        </view>
      </view>

      <view class="result-actions">
        <button class="btn-secondary" bindtap="editSmartPlan">调整</button>
        <button class="btn-primary" bindtap="applySmartPlan">创建计划</button>
      </view>
    </view>

    <!-- 简洁表单 -->
    <form bindsubmit="savePlan" class="clean-form">
      <view class="form-wrapper">

        <!-- 基本信息 -->
        <view class="form-section">
          <view class="section-title">
            <custom-icon name="airplane" size="20" color="#45B7D1" />
            <text>基本信息</text>
          </view>

          <!-- 计划标题 -->
          <view class="form-item">
            <view class="item-label">
              <text>计划标题</text>
              <text class="required">*</text>
            </view>
            <input
              class="item-input"
              placeholder="给你的旅行起个名字"
              value="{{formData.title}}"
              bindinput="onTitleInput"
              maxlength="30"
            />
          </view>

          <!-- 目的地 -->
          <view class="form-item">
            <view class="item-label">
              <text>目的地</text>
              <text class="required">*</text>
            </view>
            <view class="destination-input" bindtap="openLocationModal">
              <input
                class="item-input"
                placeholder="选择你的目的地"
                value="{{formData.destination}}"
                disabled="true"
              />
              <custom-icon name="search" size="18" color="#999" />
            </view>
          </view>

          <!-- 人数选择 -->
          <view class="form-item">
            <view class="item-label">
              <text>出行人数</text>
            </view>
            <picker
              mode="selector"
              range="{{participantOptions}}"
              value="{{participantIndex}}"
              bindchange="onParticipantChange"
            >
              <view class="picker-input">
                <text>{{participantOptions[participantIndex]}}</text>
                <custom-icon name="arrow-right" size="16" color="#999" />
              </view>
            </picker>
          </view>
        </view>

        <!-- 时间安排 -->
        <view class="form-section">
          <view class="section-title">
            <custom-icon name="calendar" size="20" color="#4ECDC4" />
            <text>时间安排</text>
          </view>

          <!-- 出发日期 -->
          <view class="form-item">
            <view class="item-label">
              <text>出发日期</text>
              <text class="required">*</text>
            </view>
            <picker
              mode="date"
              start="{{today}}"
              value="{{formData.startDate}}"
              bindchange="onStartDateChange"
            >
              <view class="picker-input">
                <text>{{formData.startDate || '选择日期'}}</text>
                <custom-icon name="calendar" size="16" color="#999" />
              </view>
            </picker>
          </view>

          <!-- 返回日期 -->
          <view class="form-item">
            <view class="item-label">
              <text>返回日期</text>
              <text class="required">*</text>
            </view>
            <picker
              mode="date"
              start="{{formData.startDate || today}}"
              value="{{formData.endDate}}"
              bindchange="onEndDateChange"
            >
              <view class="picker-input">
                <text>{{formData.endDate || '选择日期'}}</text>
                <custom-icon name="calendar" size="16" color="#999" />
              </view>
            </picker>
          </view>

          <!-- 行程天数 -->
          <view wx:if="{{duration > 0}}" class="duration-info">
            <text>共 {{duration}} 天行程</text>
          </view>
        </view>

        <!-- 可选信息 -->
        <view class="form-section">
          <view class="section-title">
            <custom-icon name="suitcase" size="20" color="#FFB74D" />
            <text>可选信息</text>
          </view>

          <!-- 预算 -->
          <view class="form-item">
            <view class="item-label">
              <text>预算</text>
            </view>
            <view class="budget-input">
              <text class="currency">¥</text>
              <input
                class="item-input budget-field"
                placeholder="预计花费"
                value="{{formData.budget}}"
                bindinput="onBudgetInput"
                type="digit"
              />
            </view>
          </view>

          <!-- 备注 -->
          <view class="form-item">
            <view class="item-label">
              <text>备注</text>
            </view>
            <textarea
              class="item-textarea"
              placeholder="记录一些特殊需求或想法..."
              value="{{formData.notes}}"
              bindinput="onNotesInput"
              maxlength="200"
              auto-height
            />
          </view>
        </view>

        <!-- 协作功能 -->
        <view class="form-section">
          <view class="section-title">
            <custom-icon name="friends" size="20" color="#FF8A65" />
            <text>协作功能</text>
          </view>

          <view class="collaboration-item">
            <view class="collaboration-info">
              <text class="collaboration-title">开启协作</text>
              <text class="collaboration-desc">邀请好友一起规划旅行</text>
            </view>
            <switch
              checked="{{enableCollaboration}}"
              bindchange="onCollaborationChange"
              color="#FF6B6B"
            />
          </view>
        </view>

      </view>

      <!-- 底部按钮 -->
      <view class="form-actions">
        <button
          class="submit-btn"
          formType="submit"
          loading="{{saving}}"
          disabled="{{saving || !formValidation.canSubmit}}"
        >
          {{saving ? '创建中...' : '创建计划'}}
        </button>
      </view>

    </form>

  </scroll-view>

  <!-- 地点输入弹窗 -->
  <location-input-modal
    show="{{showLocationModal}}"
    placeholder="你想去哪里？"
    bind:select="onLocationSelect"
    bind:close="hideLocationModal"
  />

  <!-- Toast提示 -->
  <van-toast id="van-toast" />

</view>
